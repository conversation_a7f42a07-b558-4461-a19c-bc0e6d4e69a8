import logging
import random
import csv
import io
import re
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query, get_user_id_fn
from .report import ReportManager, parse_url_key, download_report_data, generate_start_and_end_date, \
    convert_to_beijing_time
from SPAPI_Python_SDK.login_user_id import session_decorator, session_middleware, login_required_if_no_user_id

from datetime import datetime

from SPAPI_Python_SDK.retry_utils import api_retry_decorator, handle_api_errors, logger, HTTPError

SITE_LOOP = {1, 4, 6}

api_retry = api_retry_decorator(max_retries=3)

from .report import (
    ReportManager, parse_url_key, download_report_data, get_first_user_id,
    safe_create_report, safe_check_report, safe_get_report
)


def addslashes(s):
    return re.sub(r"(['\"\\])", r"\\\1", s)


@session_middleware
@login_required_if_no_user_id
def generate_reports(request):
    """
    为所有用户生成报告
    :param request:
    :return:
    """
    page_title = "每月销量-创建报告"
    return_url = request.GET.get('self', "1")
    site_id = int(request.GET.get('site', "1").strip())
    user_id = get_user_id_fn(request, site_id)
    url_key = request.GET.get('key', "")
    get_start_date = request.GET.get('StartDate', "")
    get_end_date = request.GET.get('EndDate', "")
    result = generate_start_and_end_date(get_start_date, get_end_date)
    start_date = result.get("StartDate")
    end_date = result.get("EndDate")

    if not user_id:
        return HttpResponse("没有用户")

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    try:
        # 使用上下文管理器来管理数据库连接
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:

            api_client = ReportManager(user_id, db)  # 报告API
            # 创建报告
            report_type = "GET_FBA_FULFILLMENT_CUSTOMER_SHIPMENT_SALES_DATA"
            # body = {
            # 	"reportType": report_type,
            # 	"dataStartTime": start_date,
            # 	"dataEndTime": end_date,
            # 	"marketplaceIds": ["ATVPDKIKX0DER"]
            # }

            # 2025年3月8日15:05:02
            # create_report_response = api_client.createReport(report_type, start_date, end_date)
            create_report_response = safe_create_report(
                api_client,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date
            )

            report_id = create_report_response.report_id

            # 保存report_id
            new_id = api_client.save_report_id(report_id, 'MonthSales')

            # 下一个user_id
            new_user_id = api_client.next_user()

            # 拼接url_key
            url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

            # MPR(1)、郑州(4)、武汉(6)
            site_ids_loop = [1, 4, 6]
            if site_id in site_ids_loop:
                if new_user_id:  # 有下一个用户
                    show_context = f"开始创建用户{new_user_id}的报告"
                    go_url = f"/reports/generate_reports_sale_month/?user_id={new_user_id}&site={site_id}&key={url_key}&StartDate={get_start_date}&EndDate={get_end_date}"
                    context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                    return render(request, "SPAPI_Python_SDK/goto.html", context)

            # 没有下一个用户或不是MPR，郑州，武汉表示全部建仓成功
            show_context = "全部创建成功, 开始获取报告"
            user_id = url_key.split('-')[0].split('_')[0]
            go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={get_start_date}&EndDate={get_end_date}"
            context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)
    except Exception as e:
        logging.error(f"生成报告出错: {e}")
        return HttpResponse(f"生成报告出错: {e}")


@login_required_if_no_user_id
def check_report(request):
    """
    检查报告状态
    :param request:
    :return:
    """
    page_title = "检查报告状态"
    site_id = request.GET.get('site', "").strip()
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key) if url_key else {}

    if not user_id:
        return HttpResponse("没有user")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    # 使用上下文管理器来管理数据库连接
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id !='' AND report_document_id IS NULL AND f=0 AND report_type='MonthSales' ORDER BY id DESC LIMIT 1"

            row = db.fetchone(sql)

            if not row:  # 判断有没有下一个用户
                site_ids_loop = [1, 4, 6]
                if site_id in site_ids_loop:
                    sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
                    user_rs = db.fetchone(sql)
                    if user_rs:
                        new_user_id = user_rs["id"]
                        show_context = f"开始获取下一用户的报告"
                        # user_id = url_key.split('-')[0].split('_')[0]
                        go_url = f"/reports/check_report_sale_month/?user_id={new_user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                        context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/goto.html", context)
                    else:
                        show_context = "进入最后一步-统计销量"
                        go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                        context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/goto.html", context)
                else:
                    return HttpResponse("创建完成")

            report_id = row['report_id']
            api_client = ReportManager(user_id, db)
            # 检查报告状态
            # 2025年3月8日15:06:58
            # response = api_client.checkReport(report_id)

            response = safe_check_report(api_client, report_id)
            response_dict = response.to_dict()
            status = response_dict.get("processing_status")
            created_time = response_dict.get("created_time")
            report_document_id = response_dict.get("report_document_id")

            # 根据报告状态判断下一步
            if status == "DONE":
                update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'"
                db.update(update_sqls)

                show_context = "查询成功，即将抓取"
                go_url = f"/reports/get_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                if status == "FATAL":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(5, 10)
                    show_context = f"{user_id} 报告因致命错误而停止"
                elif status == "CANCELLED":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(5, 10)
                    show_context = f"{user_id} 亚马逊数据取消"
                elif status == "IN_QUEUE":
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(10, 50)
                    show_context = f"{user_id} 报告正在处理"
                else:
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(100, 200)
                    show_context = "报告尚未开始处理"

                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
                return render(request, "SPAPI_Python_SDK/goto.html", context)


    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise  # 触发重试机制
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        raise HTTPError(str(e)) from e


@login_required_if_no_user_id
def get_report(request):
    """
    获取报告数据
    """
    page_title = "每日销量-月获取报告"
    site_id = int(request.GET.get('site'))
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key)
    if not user_id:
        return HttpResponse("没有user")

    dbdata = DB_DATA.get(site_id)
    if not dbdata:
        return HttpResponse("无效的 site_id")
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
            rs = db.fetchone(sql)

            if not rs:
                show_context = "处理完成"
                if site_id in [1, 4, 6]:
                    go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                else:
                    go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                user_id = rs["user_id"]
                zom_report_id = rs["report_id"]
                report_document_id = rs["report_document_id"]
                api_client = ReportManager(user_id, db)

                # 获取报销详情url
                # 2025年3月8日15:14:45
                # DocementResponses = api_client.getReportUrl(report_document_id)
                # 获取报告下载URL和压缩方式
                try:
                    DocementResponses = _get_report_core(api_client, report_document_id)
                    document_data = DocementResponses.to_dict()

                    report_document_id = document_data['report_document_id']
                    compression_algorithm = document_data['compression_algorithm']

                    report_url = document_data['url']

                    # 下载并解压报告数据
                    uncompressed_data = download_report_data(report_url, compression_algorithm)
                except Exception as e:
                    error_message, response_text = e.args
                    if "403" in error_message and "已过期" in response_text:
                        document_data = api_client.getReportUrl(report_document_id)
                        report_url = document_data['url']
                        uncompressed_data = download_report_data(report_url, compression_algorithm)
                    else:
                        raise  # 重新抛出异常
                if uncompressed_data:
                    data_file_like = io.StringIO(uncompressed_data)
                    csv_reader = csv.DictReader(data_file_like, delimiter='\t')

                    get_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    data_list = []
                    for cell in csv_reader:
                        shipment_date = convert_to_beijing_time(cell['shipment-date'])
                        sku = cell['sku']
                        fulfillment_center_id = cell['fulfillment-center-id', '']
                        quantity = cell['quantity', '0']
                        amazon_order_id = cell['amazon-order-id']
                        currency = cell['currency', '']
                        item_price_per_unit = float(cell['item-price-per-unit'])
                        shipping_price = float(cell['shipping-price'])
                        city = cell['ship-city']
                        state = cell['ship-state']
                        postal_code = cell['ship-postal-code']

                        data_list.append((
                            user_id, shipment_date, sku, fulfillment_center_id, quantity,
                            amazon_order_id, currency, item_price_per_unit, shipping_price,
                            city, state, postal_code
                        ))
                    if data_list:
                        sql_insert = """
							INSERT INTO fba_complete_order (
								user_id, Shipment_date, SKU, Fulfillment_center_id, Quantity, 
								`amazon-order-id`, Currency, Item_price_per_unit, Shipping_price, 
								city, state, Postal_code
							) VALUES (
								%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
							)
						"""
                        batch_size = 2000
                        total_rows = len(data_list)
                        logger.info(f"准备插入 {total_rows} 条数据，按批次大小 {batch_size} 进行。")

                        for i in range(0, total_rows, batch_size):
                            batch = data_list[i:i + batch_size]
                            try:
                                # 对每个批次执行批量插入
                                db.bulk_insert(sql_insert, batch)
                                logger.info(f"成功插入批次 {i // batch_size + 1} (从索引 {i} 到 {min(i + batch_size, total_rows) - 1}) 的 {len(batch)} 条数据。")

                            except Exception as batch_e:
                                logger.error(f"插入批次 {i // batch_size + 1} (从索引 {i} 到 {min(i + batch_size, total_rows) - 1}) 时发生错误: {batch_e}")
                                raise

                        db.bulk_insert(sql, data_list)

                    # 更新 fba_report 表
                    try:
                        sql = f"UPDATE fba_report SET f=1, get_date='{get_date}' WHERE report_id={zom_report_id}"
                        db.update(sql)
                    except Exception as e:
                        print(f"更新 fba_report 时发生错误: {e}")

                show_context = f"{user_id}报告数据抓取完成"
                go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)


    except Exception as e:
        logging.error(f"流程执行失败: {str(e)}")
        raise


@login_required_if_no_user_id
def update_report_table(request):
    page_title = "每月销量-更新报告状态"
    site_id = request.GET.get('site')
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key) if url_key else {}
    if not user_id:
        return HttpResponse("没有user")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC LIMIT 100"

        rs = db.fetchall(sql)
        #
        for row in rs:
            report_document_id = row['report_document_id']
            sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
        # db.delete(sql)

        # 检查是否有下一个用户
        site_ids_loop = [1, 4, 6]
        if site_id in site_ids_loop:
            sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
            user_rs = db.fetchone(sql)
            if user_rs:
                user_id = user_rs["id"]
                show_context = "即将查询下一个用户"
                go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                show_context = "进入最后一步"
                go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
        else:
            show_context = "每日销量更新完成"
            go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
            context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)


@login_required_if_no_user_id
def count_sku_sales(request):
    page_title = "每月销量-更新报告状态"
    site_id = request.GET.get('site')
    start_date = request.GET.get('StartDate')
    end_date = request.GET.get('EndDate')
    if not all([site_id, start_date, end_date]):
        return HttpResponse("缺少必要参数")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("未知平台")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:

        user_sql = "SELECT id FROM user where switch=1 AND MWS_Switch=1"
        users = db.fetchall(user_sql)

        # 插入统计标题
        title = f"{start_date}到{end_date}统计"
        up_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        sql = f"INSERT INTO fba_count_sku_title(Title, count_date, s_date, e_date)VALUES('{title}', '{up_date}', '{start_date}', '{end_date}');"
        new_id = db.insert(sql)

        for user in users:
            sales_sql = f"SELECT SUM(Quantity) as sales, SKU FROM fba_complete_order WHERE user_id = {user['id']} AND Shipment_date >= '{start_date} 00:00:00' AND Shipment_date <= '{end_date} 23:23:59' GROUP BY SKU"

            sku_rs = db.fetchall(sales_sql)
            for sku in sku_rs:
                insert_sql = f"INSERT INTO fba_count_sku_list(user_id, Title_id, SKU, sales)VALUES({user['id']}, {new_id}, '{sku['SKU']}', {sku['sales']});"
                db.insert(insert_sql)
        show_context = "全部更新完成"
        context = {"show_context": show_context, "page_title": page_title}
        return render(request, "SPAPI_Python_SDK/show.html", context)
